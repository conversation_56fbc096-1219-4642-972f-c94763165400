import 'package:get_it/get_it.dart';
import 'api_client.dart';
import '../providers/auth_provider.dart';
import '../providers/chat_provider.dart';

final getIt = GetIt.instance;

void setupLocator() {
  // Services
  getIt.registerLazySingleton(() => ApiClient());

  // Providers
  getIt.registerFactory(() => AuthProvider(getIt<ApiClient>()));
  getIt.registerFactory(() => ChatProvider(getIt<ApiClient>()));
}