import '../models/avatar.dart';

class AvatarService {
  static List<Avatar> getAvailableAvatars() {
    return [
      Avatar(
        id: '1',
        name: '<PERSON> the Listener',
        description: 'A compassionate companion who listens attentively',
        imagePath: 'assets/avatar.png',
      ),
      Avatar(
        id: '2',
        name: '<PERSON> the Guide',
        description: 'A knowledgeable helper for daily activities',
        imagePath: 'assets/avatar.png',
      ),
      Avatar(
        id: '3',
        name: '<PERSON> the Storyteller',
        description: 'Shares interesting stories and memories',
        imagePath: 'assets/avatar.png',
      ),
    ];
  }
}