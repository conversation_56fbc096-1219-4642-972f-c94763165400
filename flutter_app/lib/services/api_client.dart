import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/user.dart';
import '../models/otp.dart';
import '../models/message_metadata.dart';
import '../models/subscription.dart';
import 'dart:async';

const bool useMockApi = true;

class ApiException implements Exception {
  final String message;
  final int statusCode;
  final String? responseBody;

  ApiException(this.message, this.statusCode, [this.responseBody]);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';
}

class ApiClient {
  static const String _baseUrl = 'https://api.example.com/v1';
  final http.Client _client;
  String? _authToken;
  final StreamController<Map<String, dynamic>> _messageStreamController =
      StreamController.broadcast();

  ApiClient({http.Client? client}) : _client = client ?? http.Client();

  /// Parses the HTTP response and returns the decoded JSON body
  /// Throws [ApiException] for non-200 status codes
  dynamic _parseResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        return jsonDecode(response.body);
      } catch (e) {
        throw ApiException(
          'Failed to parse response: ${e.toString()}',
          response.statusCode,
        );
      }
    } else {
      throw ApiException(
        'Request failed with status ${response.statusCode}: ${response.body}',
        response.statusCode,
      );
    }
  }

  // Authentication
  Future<void> requestOTP(String phone) async {
    if (!useMockApi) {
      final url = Uri.parse('$_baseUrl/auth/otp');
      final response = await _client.post(
        url,
        headers: _getHeaders(),
        body: jsonEncode({'phone': phone}),
      );
      _parseResponse(response);
      return;
    }
    await Future.delayed(const Duration(seconds: 1));
  }

  Future<User> verifyOTP(String phone, String otp) async {
    if (!useMockApi) {
      final url = Uri.parse('$_baseUrl/auth/otp/verify');
      final response = await _client.post(
        url,
        headers: _getHeaders(),
        body: jsonEncode({'phone': phone, 'otp': otp}),
      );
      final json = _parseResponse(response);
      _authToken = json['token'];
      return User.fromJson(json);
    }
    await Future.delayed(const Duration(seconds: 1));
    _authToken = 'mock-auth-token';
    return User(
      id: 'user-123',
      phone: phone,
      email: '<EMAIL>',
      locale: 'en-US',
      tier: 'free_trial',
      createdAt: DateTime.now().toIso8601String(),
      lastSeenAt: DateTime.now().toIso8601String(),
      isActive: true,
      analyticsEnabled: false,
      onboardingCompleted: true,
    );
  }

  Future<void> logout() async {
    if (!useMockApi) {
      final url = Uri.parse('$_baseUrl/auth/logout');
      final response = await _client.post(
        url,
        headers: _getHeaders(),
      );
      _parseResponse(response);
    }
    _authToken = null;
  }

  Future<Map<String, dynamic>> sendMessage(String message) async {
    if (!useMockApi) {
      final url = Uri.parse('$_baseUrl/chat/send');
      final response = await _client.post(
        url,
        headers: _getHeaders(),
        body: jsonEncode({'message': message}),
      );
      return _parseResponse(response);
    }
    await Future.delayed(const Duration(seconds: 1));
    final response = {
      'reply': 'This is a mock response to: $message',
      'timestamp': DateTime.now().toIso8601String(),
    };
    _messageStreamController.add(response);
    return response;
  }

  Future<Map<String, dynamic>> getChatUsage() async {
    if (!useMockApi) {
      final url = Uri.parse('$_baseUrl/chat/usage');
      final response = await _client.get(
        url,
        headers: _getHeaders(),
      );
      return _parseResponse(response);
    }
    await Future.delayed(const Duration(milliseconds: 500));
    return {
      'dailyLimit': 20,
      'usedToday': 3,
      'remainingToday': 17,
      'lastUsed': DateTime.now().subtract(Duration(minutes: 30)).toIso8601String(),
    };
  }

  Future<void> deleteMemories() async {
    if (!useMockApi) {
      final url = Uri.parse('$_baseUrl/chat/memories');
      final response = await _client.delete(
        url,
        headers: _getHeaders(),
      );
      _parseResponse(response);
      return;
    }
    await Future.delayed(const Duration(seconds: 1));
  }

  Future<Map<String, dynamic>> getSimpleHealth() async {
    final url = Uri.parse('$_baseUrl/health');
    final response = await _client.get(url);
    return _parseResponse(response);
  }

  Future<Map<String, dynamic>> getComprehensiveHealth() async {
    final url = Uri.parse('$_baseUrl/api/v1/health');
    final response = await _client.get(
      url,
      headers: _getHeaders(),
    );
    return _parseResponse(response);
  }

  // User Profile
  Future<User> getCurrentUser() async {
    throw UnimplementedError('getCurrentUser() not implemented');
  }

  Future<User> updateUser(User user) async {
    throw UnimplementedError('updateUser() not implemented');
  }

  // Messages
  Future<List<MessageMetadata>> getMessages() async {
    throw UnimplementedError('getMessages() not implemented');
  }

  // Subscriptions
  Future<Subscription> getSubscription() async {
    throw UnimplementedError('getSubscription() not implemented');
  }

  Future<Subscription> updateSubscription(String tier) async {
    throw UnimplementedError('updateSubscription() not implemented');
  }

  // Helper methods
  Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      if (_authToken != null) 'Authorization': 'Bearer $_authToken',
    };
  }
}