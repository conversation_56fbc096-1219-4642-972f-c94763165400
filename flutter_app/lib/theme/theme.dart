import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class ElderlyTheme {
  static const Color primaryBlue = Color(0xFF4A90E2);
  static const Color secondaryOrange = Color(0xFFF5A623);
  static const Color accentGreen = Color(0xFF7ED321);
  static const Color background = Color(0xFFF8F8F8);
  static const Color textColor = Color(0xFF4A4A4A);
  static const Color errorRed = Color(0xFFDC3545);

  static ThemeData get themeData {
    return ThemeData(
      primaryColor: primaryBlue,
      scaffoldBackgroundColor: background,
      colorScheme: const ColorScheme.light(
        primary: primaryBlue,
        secondary: secondaryOrange,
        error: errorRed,
        background: background,
      ),
      textTheme: GoogleFonts.poppinsTextTheme(const TextTheme(
        displayLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.w500, color: textColor),
        displayMedium: TextStyle(fontSize: 22, fontWeight: FontWeight.w500, color: textColor),
        displaySmall: TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: textColor),
        bodyLarge: TextStyle(fontSize: 18, fontWeight: FontWeight.w400, color: textColor, height: 1.5),
        bodyMedium: TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: textColor, height: 1.5),
        labelLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w500, color: Colors.white),
      )),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: const Size(72, 72),
          padding: const EdgeInsets.all(16),
          textStyle: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
          backgroundColor: primaryBlue,
          foregroundColor: Colors.white,
          foregroundColor: Colors.white,
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          minimumSize: const Size(72, 72),
          padding: const EdgeInsets.all(16),
          textStyle: const TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        contentPadding: const EdgeInsets.all(16),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryBlue),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryBlue),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: primaryBlue, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorRed),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: errorRed, width: 2),
        ),
        labelStyle: const TextStyle(fontSize: 18, color: textColor),
        hintStyle: const TextStyle(fontSize: 18, color: Colors.grey),
      ),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      useMaterial3: true,
    );
  }

  static BoxDecoration get messageBubbleUser {
    return BoxDecoration(
      color: primaryBlue,
      borderRadius: BorderRadius.circular(16),
    );
  }

  static BoxDecoration get messageBubbleAI {
    return BoxDecoration(
      color: Colors.grey[200],
      borderRadius: BorderRadius.circular(16),
    );
  }
}