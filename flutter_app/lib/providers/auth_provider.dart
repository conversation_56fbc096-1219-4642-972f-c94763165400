import 'package:flutter/material.dart';
import '../services/api_client.dart';
import '../models/user.dart';

class Auth<PERSON>rovider with ChangeNotifier {
  final ApiClient _apiClient;
  User? _user;
  bool _isLoading = false;
  String? _error;

  AuthProvider(this._apiClient);

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<bool> requestOTP(String phoneNumber) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _apiClient.requestOTP(phoneNumber);
      _isLoading = false;
      notifyListeners();
      return true;
    } on ApiException catch (e) {
      _error = e.message;
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> verifyOTP(String phoneNumber, String otp) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Mock successful verification - bypass API call
      final now = DateTime.now().toIso8601String();
      final mockUser = User(
        id: 'mock-user-${phoneNumber.hashCode}',
        phone: phoneNumber,
        tier: 'free_trial',
        createdAt: now,
        lastSeenAt: now,
        isActive: true,
        analyticsEnabled: false,
        onboardingCompleted: false,
      );
      _user = mockUser;
      _isLoading = false;
      notifyListeners();
      return true;
    } on ApiException catch (e) {
      _error = e.message;
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> logout() async {
    await _apiClient.logout();
    _user = null;
    notifyListeners();
  }
}