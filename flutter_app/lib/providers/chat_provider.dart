import 'package:flutter/material.dart';

/// Manages chat state including:
/// - Current messages
/// - Loading state
/// - Selected avatar
/// - Error handling
import '../services/api_client.dart';
import '../models/message.dart';
import '../models/avatar.dart';

class Chat<PERSON><PERSON>ider with ChangeNotifier {
  final ApiClient _apiClient;
  final List<Message> _messages = [];
  bool _isLoading = false;
  String? _error;
  Avatar? _selectedAvatar;

  ChatProvider(this._apiClient);

  List<Message> get messages => _messages;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Avatar? get selectedAvatar => _selectedAvatar;

  /// Sets the currently selected avatar
  /// Notifies listeners to update dependent widgets
  void setSelectedAvatar(Avatar avatar) {
    _selectedAvatar = avatar;
    notifyListeners();
  }

  /// Sends a message and handles the response
  /// Updates messages list and loading/error states
  /// Notifies listeners after each state change
  Future<void> sendMessage(String message) async {
    _isLoading = true;
    _error = null;
    _messages.add(Message(text: message, isUser: true));
    notifyListeners();

    try {
      final response = await _apiClient.sendMessage(message);
      _messages.add(Message(text: response['reply'], isUser: false));
    } on ApiException catch (e) {
      _error = e.message;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}