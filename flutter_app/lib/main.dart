import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/service_locator.dart';
import 'providers/auth_provider.dart';
import 'providers/chat_provider.dart';
import 'screens/splash_screen.dart';
import 'screens/login_screen.dart';
import 'screens/avatar_setup_screen.dart';
import 'screens/chat_screen.dart';
import 'screens/settings_screen.dart';
import 'theme/theme.dart';

void main() {
  setupLocator();
  runApp(const ElderCareApp());
}

class ElderCareApp extends StatelessWidget {
  const ElderCareApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => getIt<AuthProvider>()),
        ChangeNotifierProvider(create: (_) => getIt<ChatProvider>()),
      ],
      child: MaterialApp(
        title: 'ElderCare',
        theme: ElderlyTheme.themeData,
        initialRoute: '/',
        routes: {
          '/': (context) => const SplashScreen(),
          '/login': (context) => const LoginScreen(),
          '/avatar-setup': (context) => const AvatarSetupScreen(),
          '/chat': (context) => const ChatScreen(),
          '/settings': (context) => const SettingsScreen(),
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
