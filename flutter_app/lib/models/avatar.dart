/// Represents a companion avatar that users can select
/// Contains avatar details like name, description and image path
class Avatar {
  final String id;
  final String name;
  final String description;
  final String imagePath;

  Avatar({
    required this.id,
    required this.name,
    required this.description,
    required this.imagePath,
  });
  @override
  String toString() {
    return 'Avatar(id: $id, name: $name)';
  }
}