class Subscription {
  final String id; // UUID
  final String tier; // "free_trial" | "core" | "annual"
  final String cycle; // "monthly" | "annual"
  final String status; // "active" | "canceled" | "expired"
  final String? startsAt; // ISO timestamp
  final String? endsAt; // ISO timestamp
  final String? canceledAt; // ISO timestamp

  Subscription({
    required this.id,
    required this.tier,
    required this.cycle,
    required this.status,
    this.startsAt,
    this.endsAt,
    this.canceledAt,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'],
      tier: json['tier'],
      cycle: json['cycle'],
      status: json['status'],
      startsAt: json['starts_at'],
      endsAt: json['ends_at'],
      canceledAt: json['canceled_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tier': tier,
      'cycle': cycle,
      'status': status,
      'starts_at': startsAt,
      'ends_at': endsAt,
      'canceled_at': canceledAt,
    };
  }
}