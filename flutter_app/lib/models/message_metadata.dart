class MessageMetadata {
  final String id; // UUID
  final String createdAt; // ISO timestamp
  final String? deliveredAt; // ISO timestamp
  final String? readAt; // ISO timestamp
  final String status; // "queued" | "sent" | "delivered" | "read" | "failed"

  MessageMetadata({
    required this.id,
    required this.createdAt,
    this.deliveredAt,
    this.readAt,
    required this.status,
  });

  factory MessageMetadata.fromJson(Map<String, dynamic> json) {
    return MessageMetadata(
      id: json['id'],
      createdAt: json['created_at'],
      deliveredAt: json['delivered_at'],
      readAt: json['read_at'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt,
      'delivered_at': deliveredAt,
      'read_at': readAt,
      'status': status,
    };
  }
}