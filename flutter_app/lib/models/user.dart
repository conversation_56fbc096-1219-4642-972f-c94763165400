class User {
  final String id; // UUID
  final String phone; // E.164 format
  final String? email; // Optional email
  final String locale; // Default: "en-US"
  final String tier; // "free_trial" | "core" | "annual"
  final String createdAt; // ISO timestamp
  final String lastSeenAt; // ISO timestamp
  final bool isActive;
  final bool analyticsEnabled;
  final bool onboardingCompleted;

  User({
    required this.id,
    required this.phone,
    this.email,
    this.locale = 'en-US',
    required this.tier,
    required this.createdAt,
    required this.lastSeenAt,
    required this.isActive,
    required this.analyticsEnabled,
    required this.onboardingCompleted,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['user_id'] ?? json['id'],
      phone: json['phone'],
      email: json['email'],
      locale: json['locale'] ?? 'en-US',
      tier: json['tier'],
      createdAt: json['created_at'],
      lastSeenAt: json['last_seen_at'],
      isActive: json['is_active'] ?? true,
      analyticsEnabled: json['analytics_enabled'] ?? false,
      onboardingCompleted: json['onboarding_completed'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phone': phone,
      'email': email,
      'locale': locale,
      'tier': tier,
      'created_at': createdAt,
      'last_seen_at': lastSeenAt,
      'is_active': isActive,
      'analytics_enabled': analyticsEnabled,
      'onboarding_completed': onboardingCompleted,
    };
  }
}