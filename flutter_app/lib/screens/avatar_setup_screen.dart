import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/avatar.dart';
import '../providers/chat_provider.dart';
import '../services/avatar_service.dart';

class AvatarSetupScreen extends StatefulWidget {
  const AvatarSetupScreen({super.key});

  @override
  State<AvatarSetupScreen> createState() => _AvatarSetupScreenState();
}

class _AvatarSetupScreenState extends State<AvatarSetupScreen> {
  final List<Avatar> avatars = AvatarService.getAvailableAvatars();

  String? selectedAvatarId;

  @override
  Widget build(BuildContext context) {
    final chatProvider = Provider.of<ChatProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Companion'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 0.8,
                ),
                itemCount: avatars.length,
                itemBuilder: (context, index) {
                  final avatar = avatars[index];
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedAvatarId = avatar.id;
                      });
                    },
                    child: Card(
                      color: selectedAvatarId == avatar.id
                          ? Colors.blue[50]
                          : Colors.white,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            Image.asset(
                              avatar.imagePath,
                              height: 100,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              avatar.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              avatar.description,
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: selectedAvatarId == null
                  ? null
                  : () {
                      final selectedAvatar = avatars.firstWhere(
                        (avatar) => avatar.id == selectedAvatarId
                      );
                      chatProvider.setSelectedAvatar(selectedAvatar);
                      Navigator.pushReplacementNamed(context, '/chat');
                    },
              child: const Text('Continue'),
            ),
          ],
        ),
      ),
    );
  }
}