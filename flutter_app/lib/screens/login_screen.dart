import 'package:flutter/material.dart';
import 'package:flutter_otp_text_field/flutter_otp_text_field.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../theme/theme.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  List<String> _otp = [];
  final _phoneController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ElderlyTheme.background,
      appBar: AppBar(
        title: const Text('Phone Verification'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Phone number input
            TextField(
              controller: _phoneController,
              decoration: InputDecoration(
                labelText: 'Phone Number',
                hintText: 'Enter your phone number',
                prefix: const Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: Text('+1 '),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              keyboardType: TextInputType.phone,
              style: const TextStyle(fontSize: 20),
            ),
            const SizedBox(height: 32),

            // OTP input field
            const SizedBox(height: 32),
            OtpTextField(
              numberOfFields: 6,
              borderColor: ElderlyTheme.primaryBlue,
              focusedBorderColor: ElderlyTheme.primaryBlue,
              showFieldAsBox: true,
              borderRadius: BorderRadius.circular(8),
              fieldWidth: 60,
              textStyle: const TextStyle(fontSize: 24),
              onCodeChanged: (String code) {
                setState(() {
                  _otp = code.split('');
                });
              },
              onSubmit: (String verificationCode) {
                _verifyOtp();
              },
            ),
            const SizedBox(height: 32),

            // Voice announcement option
            Row(
              children: [
                Checkbox(
                  value: false,
                  onChanged: (value) {
                    // TODO: Implement voice announcement preference
                  },
                ),
                const Text(
                  'Announce OTP code when received',
                  style: TextStyle(fontSize: 18),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Resend OTP button
            TextButton(
              onPressed: () {},
              child: const Text(
                'Resend OTP',
                style: TextStyle(fontSize: 18),
              ),
            ),
            const SizedBox(height: 32),

            // Verify button
            ElevatedButton(
              onPressed: _verifyOtp,
              child: const Text('Verify'),
            ),
          ],
        ),
      ),
    );
  }


  Future<void> _verifyOtp() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final otp = _otp.join();
    
    if (otp.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid 6-digit OTP')));
      return;
    }

    try {
      await authProvider.verifyOTP(
        '+1${_phoneController.text}',
        otp,
      );
      Navigator.pushReplacementNamed(context, '/avatar-setup');
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Verification failed: ${e.toString()}')));
    }
  }
}
