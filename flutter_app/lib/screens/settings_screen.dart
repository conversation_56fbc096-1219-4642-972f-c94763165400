import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/theme.dart';
import '../providers/auth_provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _darkMode = false;
  double _textScale = 1.0;
  bool _highContrast = false;
  bool _voiceAssistance = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Dark Mode Toggle
          ListTile(
            title: const Text(
              'Dark Mode',
              style: TextStyle(fontSize: 20),
            ),
            trailing: Switch(
              value: _darkMode,
              onChanged: (value) => setState(() => _darkMode = value),
              activeTrackColor: ElderlyTheme.primaryBlue.withOpacity(0.5),
              activeColor: ElderlyTheme.primaryBlue,
            ),
          ),
          const Divider(height: 32),

          // Text Size Adjustment
          const Text(
            'Text Size',
            style: TextStyle(fontSize: 20),
          ),
          Slider(
            value: _textScale,
            min: 0.8,
            max: 1.5,
            divisions: 7,
            label: _textScale.toStringAsFixed(1),
            onChanged: (value) => setState(() => _textScale = value),
            activeColor: ElderlyTheme.primaryBlue,
          ),
          const Divider(height: 32),

          // High Contrast Mode
          ListTile(
            title: const Text(
              'High Contrast Mode',
              style: TextStyle(fontSize: 20),
            ),
            trailing: Switch(
              value: _highContrast,
              onChanged: (value) => setState(() => _highContrast = value),
              activeTrackColor: ElderlyTheme.primaryBlue.withOpacity(0.5),
              activeColor: ElderlyTheme.primaryBlue,
            ),
          ),
          const Divider(height: 32),

          // Voice Assistance
          ListTile(
            title: const Text(
              'Voice Assistance',
              style: TextStyle(fontSize: 20),
            ),
            trailing: Switch(
              value: _voiceAssistance,
              onChanged: (value) => setState(() => _voiceAssistance = value),
              activeTrackColor: ElderlyTheme.primaryBlue.withOpacity(0.5),
              activeColor: ElderlyTheme.primaryBlue,
            ),
          ),
          const Divider(height: 32),

          // Save Button
          ElevatedButton(
            onPressed: () {
              // TODO: Save settings
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 60),
            ),
            child: const Text(
              'Save Settings',
              style: TextStyle(fontSize: 20),
            ),
          ),
          const SizedBox(height: 16),

          // Logout Button
          ElevatedButton(
            onPressed: () {
              context.read<AuthProvider>().logout();
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/login',
                (route) => false
              );
            },
            style: ElevatedButton.styleFrom(
              minimumSize: const Size(double.infinity, 60),
              backgroundColor: Colors.red,
            ),
            child: const Text(
              'Logout',
              style: TextStyle(fontSize: 20),
            ),
          ),
        ],
      ),
    );
  }
}