import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../lib/main.dart';
import '../lib/screens/splash_screen.dart';

void main() {
  testWidgets('App loads splash screen', (WidgetTester tester) async {
    await tester.pumpWidget(const ElderCareApp());

    // Verify splash screen elements
    expect(find.byType(SplashScreen), findsOneWidget);
    expect(find.text('ElderCare'), findsOneWidget);
    expect(find.byType(LinearProgressIndicator), findsOneWidget);
  });
}
